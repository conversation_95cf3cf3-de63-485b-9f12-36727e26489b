// Test script to check admin content API
const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:5173/api';

async function testAdminContentAPI() {
  try {
    console.log('Testing admin content API...');
    
    const response = await fetch(`${API_BASE_URL}/admin/content`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-dev-admin-bypass': 'true'
      }
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const data = await response.json();
      console.log('Success! Content data:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Error testing admin content API:', error);
  }
}

testAdminContentAPI();
